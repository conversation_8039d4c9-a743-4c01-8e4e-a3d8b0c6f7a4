package com.oplus.pay.trade.polling

import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.channel.CHANNEL_SCENE_APP_PAY
import com.oplus.pay.channel.CHANNEL_SCENE_NATIVE_PAY
import com.oplus.pay.channel.PAYMENT_TYPE_FREEPWD_PAY
import com.oplus.pay.channel.PAYMENT_TYPE_NORMAL_PAY
import com.oplus.pay.channel.model.response.V2Channel

/**
 * 支付渠道类型检查器
 * 用于判断当前支付渠道是否需要进行反查
 * 
 * 根据需求，需要反查的支付渠道类型：
 * 1. NORMAL_PAY + APP_PAY (普通支付方式)
 * 2. NORMAL_PAY + NATIVE_PAY (扫码支付方式)  
 * 3. FREEPWD_PAY (快捷支付方式)
 */
object PaymentChannelTypeChecker {
    
    private const val TAG = "PaymentChannelTypeChecker"
    
    /**
     * 支付渠道分类枚举
     */
    enum class ChannelCategory {
        NORMAL_APP_PAY,     // 普通APP支付
        NORMAL_NATIVE_PAY,  // 普通扫码支付
        FREEPWD_PAY,        // 快捷支付
        OTHER               // 其他类型，不需要反查
    }
    
    /**
     * 检查支付渠道是否需要进行反查
     * @param channel 支付渠道信息
     * @return true表示需要反查，false表示不需要反查
     */
    fun shouldPollPaymentResult(channel: V2Channel?): Boolean {
        if (channel == null) {
            PayLogUtil.w(TAG, "shouldPollPaymentResult: channel is null")
            return false
        }
        
        val category = getChannelCategory(channel)
        val shouldPoll = category != ChannelCategory.OTHER
        
        PayLogUtil.i(TAG, "shouldPollPaymentResult: paymentCode=${channel.paymentCode}, " +
                "paymentType=${channel.paymentType}, paymentScene=${channel.paymentScene}, " +
                "category=$category, shouldPoll=$shouldPoll")
        
        return shouldPoll
    }
    
    /**
     * 获取支付渠道分类
     * @param channel 支付渠道信息
     * @return 渠道分类
     */
    fun getChannelCategory(channel: V2Channel): ChannelCategory {
        val paymentType = channel.paymentType
        val paymentScene = channel.paymentScene
        
        return when {
            // 快捷支付方式
            paymentType == PAYMENT_TYPE_FREEPWD_PAY -> {
                ChannelCategory.FREEPWD_PAY
            }
            // 普通支付 + APP支付
            paymentType == PAYMENT_TYPE_NORMAL_PAY && paymentScene == CHANNEL_SCENE_APP_PAY -> {
                ChannelCategory.NORMAL_APP_PAY
            }
            // 普通支付 + 扫码支付
            paymentType == PAYMENT_TYPE_NORMAL_PAY && paymentScene == CHANNEL_SCENE_NATIVE_PAY -> {
                ChannelCategory.NORMAL_NATIVE_PAY
            }
            // 其他类型
            else -> {
                PayLogUtil.d(TAG, "getChannelCategory: unsupported channel type - " +
                        "paymentType=$paymentType, paymentScene=$paymentScene")
                ChannelCategory.OTHER
            }
        }
    }
    
    /**
     * 获取渠道分类的描述信息
     * @param category 渠道分类
     * @return 描述信息
     */
    fun getCategoryDescription(category: ChannelCategory): String {
        return when (category) {
            ChannelCategory.NORMAL_APP_PAY -> "普通APP支付"
            ChannelCategory.NORMAL_NATIVE_PAY -> "普通扫码支付"
            ChannelCategory.FREEPWD_PAY -> "快捷支付"
            ChannelCategory.OTHER -> "其他类型"
        }
    }
    
    /**
     * 检查是否为普通支付类型（包括APP支付和扫码支付）
     * @param channel 支付渠道信息
     * @return true表示是普通支付类型
     */
    fun isNormalPayType(channel: V2Channel?): Boolean {
        if (channel == null) return false
        
        val category = getChannelCategory(channel)
        return category == ChannelCategory.NORMAL_APP_PAY || category == ChannelCategory.NORMAL_NATIVE_PAY
    }
    
    /**
     * 检查是否为快捷支付类型
     * @param channel 支付渠道信息
     * @return true表示是快捷支付类型
     */
    fun isFreePwdPayType(channel: V2Channel?): Boolean {
        if (channel == null) return false
        
        val category = getChannelCategory(channel)
        return category == ChannelCategory.FREEPWD_PAY
    }
    
    /**
     * 检查是否为APP支付场景
     * @param channel 支付渠道信息
     * @return true表示是APP支付场景
     */
    fun isAppPayScene(channel: V2Channel?): Boolean {
        return channel?.paymentScene == CHANNEL_SCENE_APP_PAY
    }
    
    /**
     * 检查是否为扫码支付场景
     * @param channel 支付渠道信息
     * @return true表示是扫码支付场景
     */
    fun isNativePayScene(channel: V2Channel?): Boolean {
        return channel?.paymentScene == CHANNEL_SCENE_NATIVE_PAY
    }
    
    /**
     * 获取支付渠道的详细信息用于日志
     * @param channel 支付渠道信息
     * @return 详细信息字符串
     */
    fun getChannelDetailInfo(channel: V2Channel?): String {
        if (channel == null) return "channel is null"
        
        val category = getChannelCategory(channel)
        val categoryDesc = getCategoryDescription(category)
        
        return "paymentCode=${channel.paymentCode}, " +
                "paymentName=${channel.paymentName}, " +
                "paymentType=${channel.paymentType}, " +
                "paymentScene=${channel.paymentScene}, " +
                "category=$categoryDesc, " +
                "shouldPoll=${shouldPollPaymentResult(channel)}"
    }
    
    /**
     * 验证支付渠道信息的完整性
     * @param channel 支付渠道信息
     * @return true表示信息完整，false表示信息不完整
     */
    fun validateChannelInfo(channel: V2Channel?): Boolean {
        if (channel == null) {
            PayLogUtil.w(TAG, "validateChannelInfo: channel is null")
            return false
        }
        
        if (channel.paymentType.isNullOrEmpty()) {
            PayLogUtil.w(TAG, "validateChannelInfo: paymentType is null or empty")
            return false
        }
        
        if (channel.paymentScene.isNullOrEmpty()) {
            PayLogUtil.w(TAG, "validateChannelInfo: paymentScene is null or empty")
            return false
        }
        
        return true
    }
}
