package com.oplus.pay.trade.polling

import com.oplus.pay.basic.PayLogUtil
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 支付反查状态管理器
 * 用于管理订单的反查状态，避免重复反查
 *
 * 设计要点：
 * 1. 使用内存Map存储，不持久化
 * 2. 支持多笔订单的状态管理
 * 3. 避免内存泄漏
 */
object PaymentPollingStatusManager {
    
    private const val TAG = "PaymentPollingStatusManager"
    
    /**
     * 反查状态枚举
     */
    enum class PollingStatus {
        NOT_STARTED,    // 未开始反查
        IN_PROGRESS,    // 反查进行中
        SUCCESS,        // 反查成功
        FAILED          // 反查失败
    }
    
    /**
     * 反查状态信息
     */
    data class PollingStatusInfo(
        val orderId: String,
        val processToken: String,
        var status: PollingStatus,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 存储结构：Map<订单号, 反查状态>
     * 使用ConcurrentHashMap保证线程安全
     */
    private val pollingStatusMap = ConcurrentHashMap<String, PollingStatusInfo>()
    

    
    /**
     * 检查订单是否应该开始反查
     * @param orderId 订单号
     * @param processToken 流程token
     * @return true表示应该开始反查，false表示不应该反查（已反查过或正在反查）
     */
    fun shouldStartPolling(orderId: String, processToken: String): Boolean {
        val statusInfo = pollingStatusMap[orderId]

        return when (statusInfo?.status) {
            null, PollingStatus.NOT_STARTED -> {
                // 未开始反查，可以开始
                true
            }
            PollingStatus.IN_PROGRESS -> {
                // 正在反查中，不重复反查
                PayLogUtil.i(TAG, "shouldStartPolling: orderId=$orderId already in progress")
                false
            }
            PollingStatus.SUCCESS, PollingStatus.FAILED -> {
                // 已经反查过，不重复反查
                PayLogUtil.i(TAG, "shouldStartPolling: orderId=$orderId already polled, status=${statusInfo.status}")
                false
            }
        }
    }
    
    /**
     * 标记订单开始反查
     */
    fun markPollingStarted(orderId: String, processToken: String) {
        val statusInfo = PollingStatusInfo(orderId, processToken, PollingStatus.IN_PROGRESS)
        pollingStatusMap[orderId] = statusInfo
        PayLogUtil.i(TAG, "markPollingStarted: orderId=$orderId")
    }
    
    /**
     * 标记订单反查成功
     */
    fun markPollingSuccess(orderId: String) {
        val statusInfo = pollingStatusMap[orderId] ?: return
        statusInfo.status = PollingStatus.SUCCESS
        PayLogUtil.i(TAG, "markPollingSuccess: orderId=$orderId")
    }
    
    /**
     * 标记订单反查失败
     * @param orderId 订单号
     * @param canRetry 是否可以重试，true表示可以重试（如网络错误、activity为null等），false表示不可重试（如业务逻辑错误）
     */
    fun markPollingFailed(orderId: String, canRetry: Boolean = true) {
        val statusInfo = pollingStatusMap[orderId] ?: return

        if (canRetry) {
            // 可重试的失败，重置为未开始状态，允许下次重新尝试
            statusInfo.status = PollingStatus.NOT_STARTED
            PayLogUtil.i(TAG, "markPollingFailed: orderId=$orderId, canRetry=true, reset to NOT_STARTED")
        } else {
            // 不可重试的失败，标记为最终失败
            statusInfo.status = PollingStatus.FAILED
            PayLogUtil.i(TAG, "markPollingFailed: orderId=$orderId, canRetry=false, marked as FAILED")
        }
    }
    
    /**
     * 获取订单反查状态
     */
    fun getPollingStatus(orderId: String): PollingStatus? {
        return pollingStatusMap[orderId]?.status
    }

    /**
     * 移除订单反查状态
     */
    fun removePollingStatus(orderId: String) {
        pollingStatusMap.remove(orderId)
        PayLogUtil.i(TAG, "removePollingStatus: orderId=$orderId")
    }
    
    /**
     * 清理过期的状态信息（超过30分钟的记录）
     * 避免内存泄漏
     */
    fun cleanupExpiredStatus() {
        val currentTime = System.currentTimeMillis()
        val expireTime = 30 * 60 * 1000L // 30分钟

        val iterator = pollingStatusMap.iterator()
        while (iterator.hasNext()) {
            val entry = iterator.next()
            if (currentTime - entry.value.timestamp > expireTime) {
                iterator.remove()
                PayLogUtil.i(TAG, "cleanupExpiredStatus: removed expired status for orderId=${entry.key}")
            }
        }
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val sb = StringBuilder()
        sb.append("PaymentPollingStatusManager Debug Info:\n")
        sb.append("Active orders: ${pollingStatusMap.size}\n")
        pollingStatusMap.forEach { (orderId, statusInfo) ->
            sb.append("  - $orderId: ${statusInfo.status}\n")
        }
        return sb.toString()
    }
}
