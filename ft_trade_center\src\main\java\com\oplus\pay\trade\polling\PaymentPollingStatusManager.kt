package com.oplus.pay.trade.polling

import com.oplus.pay.basic.PayLogUtil
import java.lang.ref.WeakReference
import java.util.concurrent.ConcurrentHashMap

/**
 * 支付反查状态管理器
 * 用于管理多收银台、多订单的反查状态，避免重复反查
 * 
 * 设计要点：
 * 1. 使用内存Map存储，不持久化
 * 2. 支持多收银台场景的状态隔离
 * 3. 支持同一收银台多笔订单的状态管理
 * 4. 避免内存泄漏，使用WeakReference
 */
object PaymentPollingStatusManager {
    
    private const val TAG = "PaymentPollingStatusManager"
    
    /**
     * 反查状态枚举
     */
    enum class PollingStatus {
        NOT_STARTED,    // 未开始反查
        IN_PROGRESS,    // 反查进行中
        SUCCESS,        // 反查成功
        FAILED          // 反查失败
    }
    
    /**
     * 反查状态信息
     */
    data class PollingStatusInfo(
        val orderId: String,
        val processToken: String,
        var status: PollingStatus,
        val timestamp: Long = System.currentTimeMillis()
    )
    
    /**
     * 收银台实例信息，用于状态隔离
     */
    data class TradeCenterInstance(
        val processToken: String,
        val instanceId: String = "${processToken}_${System.currentTimeMillis()}"
    )
    
    /**
     * 存储结构：Map<收银台实例ID, Map<订单号, 反查状态>>
     * 使用ConcurrentHashMap保证线程安全
     */
    private val pollingStatusMap = ConcurrentHashMap<String, ConcurrentHashMap<String, PollingStatusInfo>>()
    
    /**
     * 收银台实例的弱引用，用于清理
     */
    private val tradeCenterInstances = ConcurrentHashMap<String, WeakReference<Any>>()
    
    /**
     * 注册收银台实例
     */
    fun registerTradeCenterInstance(processToken: String, instance: Any): String {
        val tradeCenterInstance = TradeCenterInstance(processToken)
        val instanceId = tradeCenterInstance.instanceId
        
        tradeCenterInstances[instanceId] = WeakReference(instance)
        pollingStatusMap[instanceId] = ConcurrentHashMap()
        
        PayLogUtil.i(TAG, "registerTradeCenterInstance: instanceId=$instanceId, processToken=$processToken")
        return instanceId
    }
    
    /**
     * 注销收银台实例，清理相关状态
     */
    fun unregisterTradeCenterInstance(instanceId: String) {
        tradeCenterInstances.remove(instanceId)
        pollingStatusMap.remove(instanceId)
        PayLogUtil.i(TAG, "unregisterTradeCenterInstance: instanceId=$instanceId")
    }
    
    /**
     * 检查订单是否应该开始反查
     * @param instanceId 收银台实例ID
     * @param orderId 订单号
     * @param processToken 流程token
     * @return true表示应该开始反查，false表示不应该反查（已反查过或正在反查）
     */
    fun shouldStartPolling(instanceId: String, orderId: String, processToken: String): Boolean {
        val instanceMap = pollingStatusMap[instanceId] ?: return false
        val statusInfo = instanceMap[orderId]
        
        return when (statusInfo?.status) {
            null, PollingStatus.NOT_STARTED -> {
                // 未开始反查，可以开始
                true
            }
            PollingStatus.IN_PROGRESS -> {
                // 正在反查中，不重复反查
                PayLogUtil.i(TAG, "shouldStartPolling: orderId=$orderId already in progress")
                false
            }
            PollingStatus.SUCCESS, PollingStatus.FAILED -> {
                // 已经反查过，不重复反查
                PayLogUtil.i(TAG, "shouldStartPolling: orderId=$orderId already polled, status=${statusInfo.status}")
                false
            }
        }
    }
    
    /**
     * 标记订单开始反查
     */
    fun markPollingStarted(instanceId: String, orderId: String, processToken: String) {
        val instanceMap = pollingStatusMap[instanceId] ?: return
        val statusInfo = PollingStatusInfo(orderId, processToken, PollingStatus.IN_PROGRESS)
        instanceMap[orderId] = statusInfo
        PayLogUtil.i(TAG, "markPollingStarted: instanceId=$instanceId, orderId=$orderId")
    }
    
    /**
     * 标记订单反查成功
     */
    fun markPollingSuccess(instanceId: String, orderId: String) {
        val instanceMap = pollingStatusMap[instanceId] ?: return
        val statusInfo = instanceMap[orderId] ?: return
        statusInfo.status = PollingStatus.SUCCESS
        PayLogUtil.i(TAG, "markPollingSuccess: instanceId=$instanceId, orderId=$orderId")
    }
    
    /**
     * 标记订单反查失败
     */
    fun markPollingFailed(instanceId: String, orderId: String) {
        val instanceMap = pollingStatusMap[instanceId] ?: return
        val statusInfo = instanceMap[orderId] ?: return
        statusInfo.status = PollingStatus.FAILED
        PayLogUtil.i(TAG, "markPollingFailed: instanceId=$instanceId, orderId=$orderId")
    }
    
    /**
     * 获取订单反查状态
     */
    fun getPollingStatus(instanceId: String, orderId: String): PollingStatus? {
        return pollingStatusMap[instanceId]?.get(orderId)?.status
    }
    
    /**
     * 清理过期的状态信息（超过30分钟的记录）
     * 避免内存泄漏
     */
    fun cleanupExpiredStatus() {
        val currentTime = System.currentTimeMillis()
        val expireTime = 30 * 60 * 1000L // 30分钟
        
        pollingStatusMap.forEach { (instanceId, instanceMap) ->
            val iterator = instanceMap.iterator()
            while (iterator.hasNext()) {
                val entry = iterator.next()
                if (currentTime - entry.value.timestamp > expireTime) {
                    iterator.remove()
                    PayLogUtil.i(TAG, "cleanupExpiredStatus: removed expired status for orderId=${entry.key}")
                }
            }
        }
        
        // 清理已被GC的收银台实例
        val instanceIterator = tradeCenterInstances.iterator()
        while (instanceIterator.hasNext()) {
            val entry = instanceIterator.next()
            if (entry.value.get() == null) {
                val instanceId = entry.key
                instanceIterator.remove()
                pollingStatusMap.remove(instanceId)
                PayLogUtil.i(TAG, "cleanupExpiredStatus: removed GC'd instance $instanceId")
            }
        }
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val sb = StringBuilder()
        sb.append("PaymentPollingStatusManager Debug Info:\n")
        sb.append("Active instances: ${tradeCenterInstances.size}\n")
        pollingStatusMap.forEach { (instanceId, instanceMap) ->
            sb.append("Instance $instanceId: ${instanceMap.size} orders\n")
            instanceMap.forEach { (orderId, statusInfo) ->
                sb.append("  - $orderId: ${statusInfo.status}\n")
            }
        }
        return sb.toString()
    }
}
