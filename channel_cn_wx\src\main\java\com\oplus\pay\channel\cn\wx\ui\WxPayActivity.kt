package com.oplus.pay.channel.cn.wx.ui

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.oplus.pay.channel.api.ChannelRouter
import com.oplus.pay.channel.cn.wx.ui.observer.WxPayObserver
import com.oplus.pay.channel.cn.wx.viewmodel.WxPayViewModel
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.ui.BaseActivity
import java.lang.ref.SoftReference

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright (c) 2020 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 */
class WxPayActivity : BaseActivity() {

    private val viewModel: WxPayViewModel by lazy(LazyThreadSafetyMode.NONE) {
        ViewModelProvider(this).get(WxPayViewModel::class.java)
    }

    companion object {

        fun launch(context: Context, bundle: Bundle) {
            val intent = Intent(context, WxPayActivity::class.java)
            intent.putExtras(bundle)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            context.startActivity(intent)
        }
    }

    override fun isSwitchScreenByOS(): Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initParams()
        viewModel.openChannelParams.value?.apply {
            lifecycle.addObserver(
                WxPayObserver(
                    SoftReference(this@WxPayActivity),
                    viewModel,
                )
            )
        }
    }


    private fun initParams() {
        val openChannelParams = intent.getStringExtra(ChannelRouter.EXTRA_OPEN_CHANNEL_PARAMS)
        viewModel.openChannelParams.value = openChannelParams?.let { OpenChannelParams.fromJson(it) }
    }

}