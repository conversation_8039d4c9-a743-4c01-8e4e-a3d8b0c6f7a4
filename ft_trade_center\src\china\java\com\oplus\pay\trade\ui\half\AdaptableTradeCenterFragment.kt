package com.oplus.pay.trade.ui.half

import android.annotation.SuppressLint
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.view.size
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.observe
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.alibaba.android.arouter.launcher.ARouter
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.coui.appcompat.panel.COUIBottomSheetDialog
import com.coui.appcompat.panel.COUIBottomSheetDialogFragment
import com.coui.appcompat.panel.COUIPanelFragment
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.pay.bank.ACTION_UNBIND_BANK
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.util.device.DeviceInfoHelper
import com.oplus.pay.basic.util.device.DragonflyScreenUtils
import com.oplus.pay.basic.util.ui.DisplayHelper
import com.oplus.pay.biz.ScreenType
import com.oplus.pay.config.model.response.Banner
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.marketing.model.response.CombineOrderInfo
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.TransactionConfirmationStatusCodes
import com.oplus.pay.order.PayCenterHelper
import com.oplus.pay.outcomes.OutcomesCode
import com.oplus.pay.settings.api.SettingsRouter
import com.oplus.pay.subscription.model.response.Voucher
import com.oplus.pay.trade.R
import com.oplus.pay.trade.api.TradeCenterRouter
import com.oplus.pay.trade.api.TradeCenterRouter.PRE_PARAM_BUY_PLACE
import com.oplus.pay.trade.databinding.OpayFtTradeCenterTradeFragmentFlatContentBinding
import com.oplus.pay.trade.model.PayRequest
import com.oplus.pay.trade.ui.buyplace.BuyPlaceFragment
import com.oplus.pay.trade.ui.cards.CoinListCardFragment
import com.oplus.pay.trade.ui.cards.NoticeCardFragment
import com.oplus.pay.trade.ui.cards.PayActionCardFragment
import com.oplus.pay.trade.ui.cards.PayInfoCardFragment
import com.oplus.pay.trade.ui.cards.PayTypesCardFragment
import com.oplus.pay.trade.ui.cards.QuickPayTypesCardFragment
import com.oplus.pay.trade.ui.observer.QuitDialogObserver
import com.oplus.pay.trade.ui.observer.QuitDialogObserver.Companion.isShowQuitDilaog
import com.oplus.pay.trade.usecase.PayResultBroadCastMgr
import com.oplus.pay.trade.utils.viewModelsFactory
import com.oplus.pay.trade.viewmodel.ContainerViewModel
import com.oplus.pay.trade.viewmodel.ShareStatusViewModel
import com.oplus.pay.trade.viewmodel.StaticViewModel
import com.oplus.pay.ui.BaseActivity
import com.oplus.pay.ui.recomBine
import com.oplus.pay.ui.util.menuItemClick
import java.lang.ref.SoftReference

private const val TAG = "AdaptableTradeCenterFragment"

class AdaptableTradeCenterFragment : COUIPanelFragment() {
    private lateinit var viewDataBinding: OpayFtTradeCenterTradeFragmentFlatContentBinding
    private val payReq: PayRequest by lazy {
        arguments?.getString(TradeCenterRouter.EXTRA_PAY_REQUEST)?.let {
            PayRequest.parseJson(it)
        } ?: payReq
    }
    private val shareStatusViewModel: ShareStatusViewModel by lazy(LazyThreadSafetyMode.NONE) {
        ViewModelProvider(requireActivity())[ShareStatusViewModel::class.java]
    }
    private val staticViewModel: StaticViewModel by viewModels()

    //view model
    private val viewModel by viewModelsFactory { ContainerViewModel() }
    private lateinit var localBroadCastMgr: PayResultBroadCastMgr

    /**
     * 支付结果处理关闭收银台
     */
    private fun onReceiveResult(
        processToken: String?, payOrder: String?, outcomesCode: OutcomesCode?
    ) {
        if (OutcomesCode.CODE_CANCELED == outcomesCode) {
            return
        }
        if (TextUtils.isEmpty(shareStatusViewModel.payRequestId.value)) {
            if (payReq.processToken == processToken && isAdded) {
                requireActivity().finish()
            }
        } else {
            if (payOrder == shareStatusViewModel.payRequestId.value && isAdded) {
                requireActivity().finish()
            }
        }
    }
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        initData()
        loadPayTypesCard()
        initQuitDialogObserver()
        tradeCenterExposure()
        loadData()
        setupToolbar()
        registerBroadCast()
        initFlatView()
        initObserve()
        loadPayActionCard()


        loadNoticeCard()
        loadPayInfoCard()
        if (!payReq.isRecharge) {//充值界面
            loadBuyPlaceCard()
        }
    }

    fun getRootView(): View = viewDataBinding.scrollView

    private fun initData() {
        localBroadCastMgr = PayResultBroadCastMgr(
            SoftReference(
                ::onReceiveResult
            )
        )
        shareStatusViewModel.setPayRequest(payReq)
    }

    private fun <T> loadCard(containerViewId: Int, fragment: T, mTag: String) {
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(containerViewId, fragment as Fragment, mTag)
        beginTransaction.commitNowAllowingStateLoss()
    }

    private fun findFragmentByTag(mTag: String): Fragment? {
        return childFragmentManager.findFragmentByTag(mTag)
    }

    private fun addQuitDialogObserver() {
        quitDialogObserver = QuitDialogObserver(
            mActivity = requireActivity(),
            viewModel = viewModel,
            shareStatusViewModel = shareStatusViewModel,
            payReq = payReq,
            alertQuitSelectVoucher = ::alertQuitSelectVoucher,
            normalBack = ::normalBack,
            payCenterOutDialogClick = ::payCenterOutDialogClick,
            getScreenType()
        )
        quitDialogObserver?.let {
            lifecycle.addObserver(
                it
            )
        }
    }

    private fun resetToolBar() {
        val orientation = resources.configuration?.orientation
        when (orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {//竖屏
                setupToolbar()
            }

            Configuration.ORIENTATION_LANDSCAPE -> {//横屏
                when {
                    DeviceInfoHelper.isFlat -> {  //平板
                        setupToolbar()
                    }

                    DeviceInfoHelper.isFlodDevice(requireActivity()) -> {//折叠屏
                        val hasFeature =
                            DragonflyScreenUtils.hasFeature(DragonflyScreenUtils.FEATURE_FOLD_REMAP_DISPLAY_DISABLED)
                        if (!hasFeature && DeviceInfoHelper.getFoldMode(requireActivity()) != "0") {
                            setupToolbar()
                        }
                    }
                }
            }
        }
    }

    private var quitDialogObserver: QuitDialogObserver? = null
    private fun initQuitDialogObserver() {
        addQuitDialogObserver()
        shareStatusViewModel.screenRotation.observe(viewLifecycleOwner) {
            //屏幕旋转推出弹框处理
            if (it != null) {
                resetToolBar()
                quitDialogObserver?.getQuitDialog()?.dismiss()
                if (isShowQuitDilaog) {
                    quitDialogObserver?.let { quitDialogObserver ->
                        lifecycle.removeObserver(quitDialogObserver)
                    }
                    addQuitDialogObserver()
                }
            }
        }
    }

    private fun setupToolbar() {
        getToolBar()?.let {
            it.isTitleCenterStyle = true
            (activity as AppCompatActivity).run {
                setSupportActionBar(getToolBar())
                supportActionBar?.let { actionBar ->
                    actionBar.setDisplayHomeAsUpEnabled(false)
                }
            }
            (activity as BaseActivity).initMenu(getMenuId())
            getToolBar()?.menuItemClick { item -> processMenuItemClick(item) }
        }
    }

    private fun processMenuItemClick(item: MenuItem?): Boolean {
        return when (item?.itemId) {
            R.id.setting -> {
                gotoSettingUI()
                true
            }

            R.id.cancel -> {
                cancel()
                true
            }

            else -> false
        }
    }

    private fun normalBack() {
        viewModel.notifyCancel(payReq, getScreenType())
        PayCenterHelper.cleanOrderInfo(payReq.mPartnerOrder)
        activity?.apply {
            val isOpenFlodDevice = (DeviceInfoHelper.isFlodDevice(this) &&
                    DeviceInfoHelper.getFoldMode(this) == "1")
            //平板和折叠屏打开状态从账号付款与订阅入口进入可币充值页面，付款与订阅是分屏，可币充值页面是全屏，
            //导致可币充值页面默认退场效果有闪动，此场景使用透明度渐变的退场效果
            if ((DeviceInfoHelper.isFlat && payReq.isRecharge) || (isOpenFlodDevice && payReq.isRecharge)) {
                finish()
                overridePendingTransition(
                    0,
                    R.anim.opay_ft_trade_center_exit_alpha_gradient
                )
            } else {
                finish()
            }
        }
    }

    private fun payCenterOutDialogClick(dialogClickType: String, from: String, trackId: String) {
        viewModel.payCenterOutDialogClick(payReq, getScreenType(), dialogClickType, from, trackId)
    }

    /**
     * 退出弹窗优惠券确认处理，不做抽象，页面需求自行重写
     */
    private fun alertQuitSelectVoucher(voucher: Voucher) {
        shareStatusViewModel.currentVoucher.value = voucher
    }

    private fun loadData() {
        viewModel.loadBannerInfo(payReq, getScreenType())
    }

    override fun onDestroy() {
        super.onDestroy()
        if (requireActivity().isFinishing) {  //切换暗色模式不注销广播
            unRegisterBroadCast()
        }
    }

    private fun registerBroadCast() {
        if (this::localBroadCastMgr.isInitialized) {
            viewModel.registerBroadCast(localBroadCastMgr, requireContext())
        }
    }

    private fun unRegisterBroadCast() {
        if (this::localBroadCastMgr.isInitialized) {
            viewModel.unRegisterBroadCast(localBroadCastMgr, requireContext())
        }
    }

    private fun initObserve() {
        //  fix Cannot add the same observer with different lifecyclesat androidx.lifecycle.LiveData.observe
        viewModel.bannerInfo.observe(viewLifecycleOwner, object : Observer<Resource<Banner>> {
            override fun onChanged(t: Resource<Banner>?) {
                // nothing
            }

        })
        //fix Cannot add the same observer with different lifecyclesat androidx.lifecycle.LiveData.observe
        shareStatusViewModel.attachCheckBox.observe(viewLifecycleOwner, object : Observer<Boolean> {
            override fun onChanged(it: Boolean) {
                viewModel.quitRequest.value = true
            }
        })
        viewDataBinding.tradeFlatNetError.findViewById<COUIEmptyStateView>(com.oplus.pay.ui.R.id.empty_view_group)
            .apply {
                setOnButtonClickListener {
                    LocalBroadcastManager.getInstance(requireContext())
                        .sendBroadcast(Intent().apply {
                            action = ACTION_UNBIND_BANK
                        })
                }
            }
        shareStatusViewModel.empty.observe(viewLifecycleOwner) {
            if (it) {
                val errorGroup =
                    viewDataBinding.tradeFlatNoData.findViewById<COUIEmptyStateView>(com.oplus.pay.ui.R.id.empty_view_group)
                setErrorViewHigh(errorGroup)
                // 渠道列表因为网络原因获取失败
                viewDataBinding.tradeFlatNoData.visibility = View.VISIBLE
            } else {
                viewDataBinding.tradeFlatNoData.visibility = View.GONE

            }
        }
        shareStatusViewModel.error.observe(viewLifecycleOwner) {
            if (it) {
                val errorGroup =
                    viewDataBinding.tradeFlatNetError.findViewById<COUIEmptyStateView>(com.oplus.pay.ui.R.id.empty_view_group)
                setErrorViewHigh(errorGroup)
                viewDataBinding.tradeFlatNetError.visibility = View.VISIBLE
            } else {
                viewDataBinding.tradeFlatNetError.visibility = View.GONE
            }
        }
    }

    private fun setErrorViewHigh(stateView: COUIEmptyStateView) {
        val layoutParams = stateView.layoutParams as LinearLayout.LayoutParams
        layoutParams.height = DisplayHelper.dip2px(stateView.context, 200f)
        stateView.layoutParams = layoutParams
    }

    private fun gotoSettingUI() {
        processSettingClick()
    }

    private fun cancel() {
        alertQuit()
    }

    private fun getMenuId(): Int {
        return R.menu.opay_ft_trade_center_action_menu_half
    }

    private fun getToolBar(): COUIToolbar {
        return viewDataBinding.toolbar
            .apply {
                title = getString(R.string.pay_center_new)
            }
    }

    private fun getScreenType(): String = ScreenType.FULLSCREEN.type

    private fun tradeCenterExposure() {
        if (payReq.isRecharge) {
            viewModel.kokoCoinCharge(payReq)
        } else {
            viewModel.tradeCenterExposure(payReq, getScreenType())
        }
        staticViewModel.tradeCenterPayCenterExposure(
            payRequest = payReq,
            bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
            bizCode = TransactionConfirmationStatusCodes.CODE_02_000_0001.statusCode,
            bizResult = BizResult.SUCCESS.value,
            screenType = getScreenType()

        )
    }

    private fun loadNoticeCard() {
        loadCard(R.id.fragment_speak, NoticeCardFragment(), "NoticeCardFragment")
    }

    private fun loadPayActionCard() {
        loadCard(R.id.fragment_pay_action, PayActionCardFragment(), "PayActionCardFragment")
    }

    private fun loadRechargeCoinCard() {
        loadCard(R.id.fragment_pay_info, CoinListCardFragment(), "CoinListCardFragment")
    }

    private fun loadPayInfoCard() {
        loadCard(R.id.fragment_pay_info, PayInfoCardFragment(), "CoinListCardFragment")
    }

    private fun loadPayTypesCard() {
        if (!payReq.isAutoRenew) {
            loadCard(
                R.id.fragment_channel_list,
                QuickPayTypesCardFragment.newInstance(QuickPayTypesCardFragment.QUICK_PAY_ENABLE),
                "QuickPayTypesCardFragment_QUICK_PAY_ENABLE"
            )
            loadCard(
                R.id.fragment_channel_list_disable,
                QuickPayTypesCardFragment.newInstance(QuickPayTypesCardFragment.QUICK_PAY_DISABLE),
                "QuickPayTypesCardFragment_QUICK_PAY_DISABLE"
            )
        }
        loadCard(
            R.id.fragment_channel_list_normal, PayTypesCardFragment(), "PayTypesCardFragment"
        )
    }

    private fun loadBuyPlaceCard() {
        val combineOrderInfo: CombineOrderInfo? = arguments?.getParcelable(PRE_PARAM_BUY_PLACE)
        if (combineOrderInfo != null) {
            loadCard(R.id.fragment_buy_place, BuyPlaceFragment(), "BuyPlaceFragment")
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.paycenterTime(payReq, getScreenType())
    }

    override fun onResume() {
        super.onResume()
        if (getToolBar().menu?.size == 0) {
            setupToolbar()
        }
    }
    private fun initFlatView() {
        val hasFeature =
            DragonflyScreenUtils.hasFeature(DragonflyScreenUtils.FEATURE_FOLD_REMAP_DISPLAY_DISABLED)
        //平板或折叠屏打开
        if (DeviceInfoHelper.isFlat || DeviceInfoHelper.isFlodDevice(requireActivity()) && !hasFeature && DeviceInfoHelper.getFoldMode(
                requireActivity()
            ) != "0"
        ) {
            viewDataBinding.flatContentContainer.minimumHeight =
                DisplayHelper.dip2px(requireActivity(), 500f)
        } else {
            viewDataBinding.flatContentContainer.minimumHeight = 0
        }
    }

    @SuppressLint("InflateParams", "ClickableViewAccessibility")
    override fun initView(panelView: View?) {
        toolbar.visibility = View.GONE
        // 去掉顶部带有横线
        dragView.visibility = View.GONE
        viewDataBinding = OpayFtTradeCenterTradeFragmentFlatContentBinding.inflate(
            LayoutInflater.from(requireActivity()), (contentView as? ViewGroup), false
        )
        (contentView as? ViewGroup)?.addView(viewDataBinding.root)
        setOutSideViewOnTouchListener { _, _ -> true }
        setDialogOnKeyListener { _, keyCode, event ->
            var isBack = false
            if (keyCode == KeyEvent.KEYCODE_BACK && event.action == KeyEvent.ACTION_UP) {
                alertQuit()
                isBack = true
            }
            isBack
        }
        // Check if toolbar needs to be restored after recreation
        if (getToolBar()?.menu?.size == 0) {
            setupToolbar()
        }
    }

    /**
     * 挽留弹窗逻辑处理
     */
    private fun alertQuit() {
        // 极端场景如果低内存时监听被回收，直接退出
        if (!viewModel.quitDialog.hasObservers()) {
            PayLogUtil.w(TAG, "quitDialog hasObservers size = 0")
            activity?.finish()
        }
        viewModel.quitDialog.value = true
    }

    private fun processSettingClick() {
        val defaultPayType =
            arguments?.getString(TradeCenterRouter.EXTRA_FAST_PAY_TYPE) ?: "unknown"
        val fragment = findFragmentByTag("PayTypesCardFragment") as PayTypesCardFragment
        val isHaveBankPay = fragment.isHaveBankPay()
        viewModel.settingClick(payReq)
        val bundle = viewModel.getGoToSettingUIBundle(
            payReq,
            shareStatusViewModel.actualAmount.value ?: "",
            defaultPayType,
            isHaveBankPay,
            getScreenType()
        )
        ARouter.getInstance().build(SettingsRouter.PATH_PATH_MAIN)
            .with(bundle.recomBine(payReq.mPayId)).navigation(requireActivity())
    }

    /**
     * 高端控件面板需要强指定颜色
     */
    override fun onShow(isShowOnFirstPanel: Boolean?) {
        super.onShow(isShowOnFirstPanel)
        ((parentFragment as? COUIBottomSheetDialogFragment)?.dialog as? COUIBottomSheetDialog)?.setPanelBackgroundTintColor(
            ContextCompat.getColor(
                requireActivity(),
                com.support.appcompat.R.color.coui_color_background_elevatedWithCard
            )
        )
    }
}