package com.oplus.pay.trade.polling

import android.app.Dialog
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.channel.ChannelHelper
import com.oplus.pay.channel.model.response.V2Channel
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.outcomes.model.OutcomesPrePayResponse
import com.oplus.pay.trade.model.PayRequest
import com.oplus.pay.trade.viewmodel.ShareStatusViewModel
import com.oplus.pay.ui.DialogHelper
import java.lang.ref.SoftReference

/**
 * 支付反查管理器
 * 负责执行具体的反查网络请求和Loading管理
 *
 * 功能：
 * 1. 管理Loading对话框的显示和隐藏
 * 2. 执行支付结果反查网络请求
 * 3. 处理反查结果，成功时跳转到支付结果页
 * 4. 处理反查失败，隐藏Loading对话框
 */
class PaymentPollingManager(
    private val activityRef: SoftReference<FragmentActivity>,
) {

    private val TAG = "PaymentPollingManager"

    /**
     * Loading对话框
     */
    private var loadingDialog: Dialog? = null

    /**
     * 反查提供者
     */
    private var channelQueryProvider: IChannelQueryPayResult? = null

    /**
     * 反查结果回调
     */
    interface PollingCallback {
        fun onPollingSuccess(result: OutcomesPrePayResponse?)
        fun onPollingFailed(errorCode: String?, errorMsg: String?)
    }

    /**
     * 开始反查
     * @param orderId 订单号
     * @param channel 支付渠道
     * @param processToken 流程token
     * @param callback 反查结果回调
     */
    fun startPolling(
        payResult: PayRequest?,
        orderId: String,
        contractCode: String,
        channel: V2Channel,
        processToken: String,
        callback: PollingCallback
    ) {
        PayLogUtil.i(TAG, "startPolling: orderId=$orderId, channel=${channel.paymentCode}")

        val activity = activityRef.get()
        if (activity == null) {
            PayLogUtil.e(TAG, "startPolling: activity is null")
            callback.onPollingFailed("ACTIVITY_NULL", "Activity is null")
            return
        }

        // 显示Loading对话框
        showLoadingDialog()

        // 方式1：使用IChannelQueryPayResult接口（如果支持的话）
        tryChannelQueryPolling(payResult, orderId, channel, processToken, contractCode, callback)

        // 方式2：如果方式1不支持，则使用直接的Outcomes查询
        // tryDirectOutcomesQuery(orderId, channel, processToken, callback)
    }

    /**
     * 尝试使用IChannelQueryPayResult接口进行反查
     */
    private fun tryChannelQueryPolling(
        payResult: PayRequest?,
        orderId: String,
        channel: V2Channel,
        processToken: String,
        contractCode: String,
        callback: PollingCallback
    ) {
        try {
            // 初始化反查提供者
            channelQueryProvider = ChannelHelper.initChannelQueryPayObserver(
                activityRef = activityRef,
                payType = channel.paymentCode ?: ""
            )

            // 开始反查
            channelQueryProvider?.startQueryChannelResult(
                pollingEndAndNotifyPaySuccessResultFunc = {
                    PayLogUtil.i(TAG, "channelQueryPolling: success callback")
                    hideLoadingDialog()
                    // 这里需要获取实际的反查结果，暂时传null
                    callback.onPollingSuccess(null)
                },
                pollingEndAndNotifyPayErrorResultFunc = {
                    PayLogUtil.w(TAG, "channelQueryPolling: failed callback")
                    hideLoadingDialog()
                    callback.onPollingFailed("CHANNEL_QUERY_FAILED", "Channel query failed")
                },
                countryCode = payResult?.mCountryCode,
                prePayToken = payResult?.prePayToken,
                payReqId = orderId,
                contractCode = contractCode
            )
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "tryChannelQueryPolling: exception" + e.message)
            hideLoadingDialog()
            callback.onPollingFailed("EXCEPTION", e.message)
        }
    }

    /**
     * 显示Loading对话框
     */
    private fun showLoadingDialog() {
        try {
            val activity = activityRef.get() ?: return

            if (loadingDialog?.isShowing == true) {
                PayLogUtil.d(TAG, "showLoadingDialog: dialog already showing")
                return
            }

            // 使用DialogHelper.createColorStyleProgressingDialog显示带指定文案的Loading对话框
            loadingDialog = DialogHelper.createColorStyleProgressingDialog(
                activity,
                com.oplus.pay.ui.R.string.qmf_confirm_payment_result
            )
            loadingDialog?.apply {
                setCanceledOnTouchOutside(false)
                setCancelable(true) // 允许用户取消
                show()
            }

            PayLogUtil.i(TAG, "showLoadingDialog: dialog shown with qmf_confirm_payment_result")
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "showLoadingDialog: exception" + e.message)
        }
    }

    /**
     * 隐藏Loading对话框
     */
    private fun hideLoadingDialog() {
        try {
            // 使用LoadingDialogHelper隐藏Loading对话框
            com.oplus.pay.ui.util.LoadingDialogHelper.dismissLoadingDialog()
            PayLogUtil.i(TAG, "hideLoadingDialog: dialog dismissed")
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "hideLoadingDialog: exception" + e.message)
        }
    }

    /**
     * 停止反查
     */
    fun stopPolling() {
        PayLogUtil.i(TAG, "stopPolling: stopping polling")
        hideLoadingDialog()
        channelQueryProvider = null
    }

    /**
     * 清理资源
     */
    fun cleanup() {
        PayLogUtil.i(TAG, "cleanup: cleaning up resources")
        stopPolling()
    }
}
