package com.oplus.pay.channel.poll

import android.app.Dialog
import android.os.Handler
import android.os.Looper
import android.widget.FrameLayout
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LiveData
import androidx.lifecycle.MediatorLiveData
import com.airbnb.lottie.LottieAnimationView
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.livedata.AbsentLiveData
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.config.ConfigHelper
import com.oplus.pay.outcomes.OutcomesHelper
import com.oplus.pay.outcomes.QueryChannelStatusParam
import com.oplus.pay.outcomes.model.OutcomesChannelStatusResponse
import com.oplus.pay.ui.DialogHelper
import java.lang.ref.SoftReference

/**
 * <p>Title: </p>
 * <p>Description: </p>
 * <p>Copyright (c) 2020 www.oppo.com Inc. All rights reserved.</p>
 * <p>Company: OPPO</p>
 *
 * <AUTHOR>
 * @version 1.0
 */

private const val TAG = "QueryChannelResultObserver"

class QueryChannelResultObserver(
    /**
     * 上下文，反查依赖的容器组件
     */
    private val activityRef: SoftReference<FragmentActivity>,
    /**
     * 打开渠道参数
     */
    private val openChannelParams: OpenChannelParams?,
    /**
     * 查询渠道反查时长限制
     */
    private val payType: String
): IChannelQueryPayResult {

    private val loadingDialog: Dialog? by lazy(LazyThreadSafetyMode.NONE) {
        val dialog = activityRef.get()?.let {
            DialogHelper.splashLoadingDialog(
                it,
                com.oplus.pay.ui.R.style.loading_dialog,
                com.oplus.pay.ui.R.layout.opay_lib_ui_activity_splash_loading_bg,
                com.oplus.pay.ui.R.drawable.opay_lib_ui_pp_loading_shape_bg,
                com.oplus.pay.ui.R.id.iv_loading
            )
        }
        dialog?.setCanceledOnTouchOutside(false)
        dialog
    }

    override fun startQueryChannelResult(
        pollingEndAndNotifyPaySuccessResultFunc: () -> Unit,
        pollingEndAndNotifyPayErrorResultFunc: () -> Unit
    ) {
        val info = ConfigHelper.getChannelQueryConfig(
            countryCode = openChannelParams?.bizExt?.countryCode ?: "",
            payType = payType
        )
        PayLogUtil.i(TAG, "query config info:${info.toString()}")
        showLoadingDialog()
        // 超时后关闭loading，并标记超时
        var isTimeout = false
        Handler(Looper.getMainLooper()).postDelayed({
            isTimeout = true
            dismissLoading()
            activityRef.get()?.finish()
            PayLogUtil.i(TAG, "time: ${info?.queryTime?.toLong()} over, dissmissloading and finish")
        }, info?.queryTime?.toLong() ?: 500)
        queryChannelPayResult(
            prePayToken= openChannelParams?.bizExt?.prePayToken ?: "",
            contractCode= openChannelParams?.bizExt?.contractCode ?: "",
            payReqId= openChannelParams?.payReqId ?: "",
            countryCode= openChannelParams?.bizExt?.countryCode ?: "",
        ).observe(activityRef.get() as LifecycleOwner) {
            if (isTimeout) {
                PayLogUtil.w(TAG, "timeout not processed")
                return@observe
            }
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(TAG, "queryPayResult SUCCESS and notifyPayResult Suc")
                    dismissLoading()
                    pollingEndAndNotifyPaySuccessResultFunc.invoke()
                }

                Status.ERROR -> {
                    PayLogUtil.e(TAG, "queryPayResult ERROR and notifyPayResult Fail")
                    dismissLoading()
                    pollingEndAndNotifyPayErrorResultFunc.invoke()
                }

                else -> {}
            }
        }
    }

    /**
     * 查询渠道支付结果信息
     * @param payOrder 支付订单
     * @param payType 支付方式
     * @param bizExt 业务扩展参数
     */
    fun queryChannelPayResult(
        prePayToken: String,
        contractCode: String,
        payReqId: String,
        countryCode: String
    ): LiveData<Resource<String?>> {

        val payResult = MediatorLiveData<Resource<String?>>()

        val param = QueryChannelStatusParam(
            prePayToken= prePayToken,
            contractCode= contractCode,
            payReqId= payReqId,
            countryCode= countryCode,
        )

        val ret = queryChannelPayResult(param)
        payResult.addSource(ret) {
            when (it.status) {
                Status.SUCCESS -> {
                    PayLogUtil.i(TAG, "channelStatus:${it.data?.channelStatus}")
                    if (it.data?.channelStatus == true) {
                        payResult.value = Resource.success()
                    } else {
                        payResult.value = Resource.error(msg = "channel status not ok", code = "")
                    }
                }

                Status.ERROR -> {
                    PayLogUtil.e(TAG, "channelStatus#ERROR:${it.message} ${it.code}")
                    payResult.value = Resource.error(msg = "${it.message}", code = "${it.code}")
                }

                else -> {
                    // nothing
                }
            }
        }

        return payResult
    }

    /**
     * 查询渠道支付状态
     */
    fun queryChannelPayResult(param: QueryChannelStatusParam?): LiveData<Resource<OutcomesChannelStatusResponse>> {
        return param?.let {
            OutcomesHelper.queryChannelStatusResult(it)
        } ?: AbsentLiveData.create()
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        loadingDialog?.findViewById<LottieAnimationView>(com.oplus.pay.ui.R.id.iv_loading)?.apply {
            visibility = FrameLayout.GONE
            pauseAnimation()
            cancelAnimation()
        }
    }

    private fun showLoadingDialog() {
        if (activityRef.get()?.isFinishing == true) {
            return
        }
        loadingDialog?.let {
            it.show()
        }
    }

    private fun dismissLoading() {
        loadingDialog?.apply {
            if (isShowing) {
                this.dismiss()
            }
        }
    }

}