package com.oplus.pay.channel.cn.wx.ui.observer

import android.app.Activity
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import androidx.appcompat.app.AlertDialog
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.google.gson.Gson
import com.oplus.pay.basic.AppRuntime
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.Status
import com.oplus.pay.basic.util.serialize.GSON
import com.oplus.pay.basic.util.ui.ToastUtil
import com.oplus.pay.biz.BizResultType
import com.oplus.pay.biz.TransType
import com.oplus.pay.channel.ChannelHelper
import com.oplus.pay.channel.StatResultId
import com.oplus.pay.channel.cn.statistic.StaticHelper
import com.oplus.pay.channel.cn.wx.ACTION_WECHAT_PAY
import com.oplus.pay.channel.cn.wx.ACTION_WECHAT_SIGN
import com.oplus.pay.channel.cn.wx.CODE_QUERY_SUC
import com.oplus.pay.channel.cn.wx.CODE_UNKNOWN
import com.oplus.pay.channel.cn.wx.EXTRA_WECHAT_PAY_OBSERVER_NOTIFY
import com.oplus.pay.channel.cn.wx.EXT_PAY_REQUEST_ID
import com.oplus.pay.channel.cn.wx.EXT_PAY_REQUEST_PACKAGE
import com.oplus.pay.channel.cn.wx.MSG_NO_ERROR
import com.oplus.pay.channel.cn.wx.PAYMENT_CODE_WX_PAY
import com.oplus.pay.channel.cn.wx.R
import com.oplus.pay.channel.cn.wx.WxChannelHandler
import com.oplus.pay.channel.cn.wx.WxPayHelper
import com.oplus.pay.channel.cn.wx.WxpayParam
import com.oplus.pay.channel.cn.wx.broadcast.WxPayResultBroadCast
import com.oplus.pay.channel.cn.wx.ui.OldFashionedActivity
import com.oplus.pay.channel.cn.wx.viewmodel.WxPayViewModel
import com.oplus.pay.channel.model.CHANNEL_NOT_SUPPORT
import com.oplus.pay.channel.model.TRANS_MODE_OLD_FASHIONED
import com.oplus.pay.channel.model.request.OpenChannelParams
import com.oplus.pay.channel.polling.IChannelPollPayResult
import com.oplus.pay.channel.polling.IChannelQueryPayResult
import com.oplus.pay.config.ConfigHelper
import com.oplus.pay.monitor.api.d
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.ChannelResultStatusCodes
import com.oplus.pay.monitor.api.model.ChannelRoutingStatusCodes
import com.oplus.pay.outcomes.ACTION_OUTCOMES
import com.oplus.pay.ui.DialogHelper
import com.oplus.pay.ui.widget.addOnWindowAttachListener
import com.platform.usercenter.trace.rumtime.AutoTrace
import com.tencent.mm.opensdk.modelbase.BaseReq
import com.tencent.mm.opensdk.modelbiz.OpenWebview
import com.tencent.mm.opensdk.modelbiz.WXOpenBusinessWebview
import com.tencent.mm.opensdk.modelpay.PayReq
import com.usercenter.custom.trace.WxChannelSceneTechTrace
import org.json.JSONObject
import java.lang.ref.SoftReference
import java.lang.ref.WeakReference
import java.util.HashMap

class WxPayObserver (
    private val activityRef: SoftReference<FragmentActivity>,
    private val viewModel: WxPayViewModel
) : DefaultLifecycleObserver {

    companion object {
        private const val TAG = "WxPayObserver"
        private val ERROR_OPEN_FAIL = "open wxPay fail"
        private val ERROR_QUERY_FAIL = "query wxPay fail"
    }

    private val openChannelParams: OpenChannelParams? by lazy(LazyThreadSafetyMode.NONE) {
        viewModel.openChannelParams.value
    }

    private val outComesReceiver: BroadcastReceiver by lazy(LazyThreadSafetyMode.NONE) {
        WxPayResultBroadCast(activityRef, viewModel)
    }

    private val queryChannelPayResultObserver: IChannelQueryPayResult? by lazy {
        ChannelHelper.initChannelQueryPayObserver(
            activityRef = activityRef,
            openChannelParams = openChannelParams,
            payType = PAYMENT_CODE_WX_PAY
        )
    }

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
        activityRef.get()?.apply {
            LocalBroadcastManager.getInstance(this).registerReceiver(outComesReceiver, IntentFilter().apply {
                addAction(ACTION_WECHAT_PAY)
                addAction(ACTION_WECHAT_SIGN)
                addAction(ACTION_OUTCOMES)
            })
        }
        start()
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        // 从渠道返回透明页
        if (viewModel.onPause.value == true && viewModel.openChannel.value == true) {
            viewModel.onPause.value = false
            viewModel.openChannel.value = false
            doChannelPollingAction(owner)
        }

    }

    private fun doChannelPollingAction(owner: LifecycleOwner) {

        if (viewModel.openChannelParams.value?.bizExt?.isChannelPayTradeCenter ==  BizResultType.N.value) {
            PayLogUtil.i(TAG, "isNormalPayOrder false")
            activityRef.get()?.finish()
        } else {
            queryChannelPayResultObserver?.startQueryChannelResult (
                pollingEndAndNotifyPaySuccessResultFunc = {
                    notifyPayResult(false, EXTRA_WECHAT_PAY_OBSERVER_NOTIFY, CODE_QUERY_SUC, MSG_NO_ERROR)
                },
                pollingEndAndNotifyPayErrorResultFunc = {
                    notifyPayResult(false, EXTRA_WECHAT_PAY_OBSERVER_NOTIFY, CODE_UNKNOWN, ERROR_QUERY_FAIL)
                }
            )
        }
    }

    private fun start() {
        if (isSupport()) {
            val requestMsg: String = openChannelParams?.channelOrder?.channelData ?: ""
            if (openChannelParams?.channelExtras?.transType != null
                && TransType.SIGN.type == openChannelParams?.channelExtras?.transType
            ) {
                if (TRANS_MODE_OLD_FASHIONED == openChannelParams?.channelExtras?.transMode) {
                    // 旧的云服务签约并支付
                    activityRef.get()
                        ?.let { activity ->
                            openChannelParams?.let {
                                viewModel.openChannel.value = true
                                startOldFashionedSign(activity, requestMsg, it)
                            }
                        }
                } else {
                    // 纯签约的订单
                    openChannelParams?.let {
                        viewModel.openChannel.value = true
                        startAppSign(requestMsg, it)
                    }
                }
            } else {
                if (openChannelParams?.channelExtras?.serverTradeType== TransType.SIGN.type) {//消费签约 0元纯签约
                    // 纯签约的订单
                    openChannelParams?.let {
                        viewModel.openChannel.value = true
                        startAppSign(requestMsg, it)
                    }
                } else {
                    // 正常支付/签约并支付的订单
                    openChannelParams?.let {
                        viewModel.openChannel.value = true
                        startNormalPay(requestMsg, it)
                    }
                }
            }
        } else {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams?.bizExt?.processToken  ?: "",
                order = openChannelParams?.bizExt?.partnerOrder  ?: "",
                prePayToken = openChannelParams?.bizExt?.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_ROUTING.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams?.bizExt?.contractType),
                bizResult = BizResult.ERROR.value,
                bizCode = ChannelRoutingStatusCodes.CODE_03_000_0007.statusCode,
                bizErrorMsg = "wx not support",
                routingMethod = "pay",
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams?.bizExt?.partnerCode ?: "")
                    put("channelCode",openChannelParams?.channelCode ?: "")
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelRoutingEnd(buildCommonMap)

            openChannelStart(openChannelParams, StatResultId.FAIL.value)
            ToastUtil.show(R.string.install_new_wechat)
            notifyPayResult(false, EXTRA_WECHAT_PAY_OBSERVER_NOTIFY, CHANNEL_NOT_SUPPORT, "weChat pay not support")
        }
    }

    private fun isSupport(): Boolean {
        return WxPayHelper.isSupport()
    }

    private fun startOldFashionedSign(
        activity: Activity,
        requestMsg: String,
        openChannelParams: OpenChannelParams,
    ) {
        // 中转页面，用于查询签约结果
        OldFashionedActivity.launch(activity, openChannelParams)

        val req = OpenWebview.Req()
        req.url = requestMsg
        openWxApi(req, openChannelParams)
    }

    private fun startNormalPay(
        requestMsg: String,
        openChannelParams: OpenChannelParams,
    ) {
        val mWxParam = WxpayParam(requestMsg)
        val req: PayReq =
            getPayReq(mWxParam, openChannelParams.payReqId, openChannelParams.appPackage)
        openWxApi(req, openChannelParams)
    }

    /**
     * 纯签约 -- APP签约
     */
    private fun startAppSign(
        requestMsg: String,
        openChannelParams: OpenChannelParams,
    ) {
        val req = WXOpenBusinessWebview.Req()
        req.businessType = 12 //固定值
        val queryInfo = HashMap<String, String>()
        queryInfo["pre_entrustweb_id"] = requestMsg
        req.queryInfo = queryInfo

        openWxApi(req, openChannelParams)
    }

    /**
     * 获取微信支付请求的参数
     */
    private fun getPayReq(wxParam: WxpayParam, payRequestId: String, appPackage: String): PayReq {
        val req = PayReq()
        req.appId = wxParam.appid
        req.partnerId = wxParam.partnerid
        req.prepayId = wxParam.prepayid
        req.packageValue = wxParam.packageNm
        req.nonceStr = wxParam.noncestr
        req.timeStamp = wxParam.timestamp
        req.sign = wxParam.sign

        val packageName: String =
            if (!TextUtils.isEmpty(appPackage)) appPackage else wxParam.packageNm
        req.extData = getWeChatExtData(payRequestId, packageName)
        PayLogUtil.i(TAG, "prepayId=" + req.prepayId + ",extData=" + req.extData)
        return req
    }

    /**
     * 附加参数
     */
    private fun getWeChatExtData(payRequestId: String?, packageName: String?): String? {
        val map: HashMap<String?, String?> = HashMap()
        if (!TextUtils.isEmpty(payRequestId)) {
            map[EXT_PAY_REQUEST_ID] = payRequestId
        }
        if (!TextUtils.isEmpty(packageName)) {
            map[EXT_PAY_REQUEST_PACKAGE] = packageName
        }
        return Gson().toJson(map)
    }

    /**
     * 打开微信支付api
     */
    private fun openWxApi(
        req: BaseReq,
        openChannelParams: OpenChannelParams
    ) {
        PayLogUtil.i(TAG, "open weChat pay")
        val buildCommonMap = StaticHelper.buildCommonMap(
            processToken = openChannelParams.bizExt.processToken,
            order = openChannelParams.bizExt.partnerOrder,
            prePayToken = openChannelParams.bizExt.prePayToken ?: "",
            bizNode = BizNode.CHANNEL_CONFIRMATION.value,
            payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
            bizResult = BizResult.SUCCESS.value,
            bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode,
            bizErrorMsg = "",
            payType = StaticHelper.getPayType(openChannelParams),
            outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                put("partnerCode",openChannelParams.bizExt.partnerCode)
                put("channelCode",openChannelParams.channelCode)
                put("transType",openChannelParams.channelExtras.transType ?: "")
                put("transMode",openChannelParams.channelExtras.transMode ?: "")
                put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
            })
        )
        PayLogUtil.d(TAG, buildCommonMap)
        StaticHelper.eventIdChannelPayStart(buildCommonMap)
        try {
            if (!WxPayHelper.getOpenApi(AppRuntime.getAppContext()).sendReq(req)) {
                notifyPayResult(false, EXTRA_WECHAT_PAY_OBSERVER_NOTIFY, CODE_UNKNOWN, ERROR_OPEN_FAIL)
                openChannelStart(openChannelParams, StatResultId.FAIL.value)
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = openChannelParams.bizExt.processToken,
                    order = openChannelParams.bizExt.partnerOrder,
                    prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                    bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                    payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                    bizResult = BizResult.ERROR.value,
                    bizErrorMsg = "${CODE_UNKNOWN}--${ERROR_OPEN_FAIL}",
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0001.statusCode,
                    payType = StaticHelper.getPayType(openChannelParams),
                    outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                        put("partnerCode",openChannelParams.bizExt.partnerCode)
                        put("channelCode",openChannelParams.channelCode)
                        put("transType",openChannelParams.channelExtras.transType ?: "")
                        put("transMode",openChannelParams.channelExtras.transMode ?: "")
                        put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            } else {
                val buildCommonMap = StaticHelper.buildCommonMap(
                    processToken = openChannelParams.bizExt.processToken,
                    order = openChannelParams.bizExt.partnerOrder,
                    prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                    bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                    payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                    bizResult = BizResult.SUCCESS.value,
                    bizCode = ChannelResultStatusCodes.CODE_04_000_0000.statusCode,
                    bizErrorMsg = "",
                    payType = StaticHelper.getPayType(openChannelParams),
                    outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                        put("partnerCode",openChannelParams.bizExt.partnerCode)
                        put("channelCode",openChannelParams.channelCode)
                        put("transType",openChannelParams.channelExtras.transType ?: "")
                        put("transMode",openChannelParams.channelExtras.transMode ?: "")
                        put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                    })
                )
                PayLogUtil.d(TAG, buildCommonMap)
                StaticHelper.eventIdChannelPayEnd(buildCommonMap)
                openChannelStart(openChannelParams, StatResultId.SUCCESS.value)
            }
        } catch (e: Exception) {
            val buildCommonMap = StaticHelper.buildCommonMap(
                processToken = openChannelParams.bizExt.processToken,
                order = openChannelParams.bizExt.partnerOrder,
                prePayToken = openChannelParams.bizExt.prePayToken ?: "",
                bizNode = BizNode.CHANNEL_CONFIRMATION.value,
                payBizScene = StaticHelper.getPayBizScene(openChannelParams.bizExt.contractType),
                bizResult = BizResult.ERROR.value,
                bizErrorMsg = "${CODE_UNKNOWN}--${e.message}",
                bizCode = ChannelResultStatusCodes.CODE_04_000_0001.statusCode,
                payType = StaticHelper.getPayType(openChannelParams),
                outputResult = GSON.toJson(mutableMapOf<String,String>().apply {
                    put("partnerCode",openChannelParams.bizExt.partnerCode)
                    put("channelCode",openChannelParams.channelCode)
                    put("transType",openChannelParams.channelExtras.transType ?: "")
                    put("transMode",openChannelParams.channelExtras.transMode ?: "")
                    put("serverTradeType",openChannelParams.channelExtras.serverTradeType ?: "")
                })
            )
            PayLogUtil.d(TAG, buildCommonMap)
            StaticHelper.eventIdChannelPayEnd(buildCommonMap)
            notifyPayResult(false, EXTRA_WECHAT_PAY_OBSERVER_NOTIFY, CODE_UNKNOWN, ERROR_OPEN_FAIL)
            openChannelStart(openChannelParams, StatResultId.FAIL.value)
        }
    }

    private fun openChannelStart(openChannelParams: OpenChannelParams?, resultId: String) {
        AutoTrace.get().upload(
            WxChannelSceneTechTrace.openChannelStart(
                countryCode = openChannelParams?.bizExtra?.countryCode ?: "",
                source = openChannelParams?.bizExtra?.source ?: "",
                order = openChannelParams?.bizExtra?.partnerOrder ?: "",
                token = openChannelParams?.bizExtra?.processToken ?: "",
                partnerId = openChannelParams?.bizExtra?.partnerCode ?: "",
                payType = openChannelParams?.paymentCode ?: "",
                channelOrder = "",
                resultId = resultId
            )
        )
    }

    private fun notifyPayResult(useCancel: Boolean = true, actionStr: String, errorCode: Int, errorMsg: String) {
        viewModel.notifyPayResult(viewModel.openChannelParams.value?.toJson(), useCancel, actionStr, errorCode, errorMsg)
        activityRef.get()?.finish()
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
        viewModel.onPause.value = true
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        activityRef.get()?.apply {
            viewModel.unRegisterOutComeReceiver(this, outComesReceiver)
        }
    }


}