package com.oplus.pay.trade.ui.across
import android.app.Activity
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.LinearLayout
import android.widget.RelativeLayout
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.size
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.ViewModelProvider
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.alibaba.android.arouter.launcher.ARouter
import com.coui.appcompat.emptyview.COUIEmptyStateView
import com.coui.appcompat.toolbar.COUIToolbar
import com.oplus.pay.bank.ACTION_UNBIND_BANK
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.basic.Resource
import com.oplus.pay.basic.util.device.DeviceInfoHelper
import com.oplus.pay.basic.util.ui.DisplayHelper
import com.oplus.pay.biz.ScreenType
import com.oplus.pay.biz.model.BizExt
import com.oplus.pay.config.model.response.Banner
import com.oplus.pay.marketing.model.response.CombineOrderInfo
import com.oplus.pay.monitor.api.model.BizNode
import com.oplus.pay.monitor.api.model.BizResult
import com.oplus.pay.monitor.api.model.TransactionConfirmationStatusCodes
import com.oplus.pay.order.PayCenterHelper
import com.oplus.pay.outcomes.OutcomesCode
import com.oplus.pay.settings.api.SettingsRouter
import com.oplus.pay.subscription.model.response.Voucher
import com.oplus.pay.trade.R
import com.oplus.pay.trade.api.TradeCenterRouter
import com.oplus.pay.trade.api.TradeCenterRouter.PRE_PARAM_BUY_PLACE
import com.oplus.pay.trade.databinding.OpayFtTradeCenterFragmentAcrossTradeCenterBinding
import com.oplus.pay.trade.model.PayRequest
import com.oplus.pay.trade.ui.buyplace.AcrossBuyPlaceFragment
import com.oplus.pay.trade.ui.cards.AcrossLeftPayActionCardFragment
import com.oplus.pay.trade.ui.cards.CoinListCardFragment
import com.oplus.pay.trade.ui.cards.PayActionCardFragment
import com.oplus.pay.trade.ui.cards.PayInfoCardFragment
import com.oplus.pay.trade.ui.cards.PayTypesCardFragment
import com.oplus.pay.trade.ui.cards.QuickPayTypesCardFragment
import com.oplus.pay.trade.ui.observer.QuitDialogObserver
import com.oplus.pay.trade.ui.observer.QuitDialogObserver.Companion.isShowQuitDilaog
import com.oplus.pay.trade.usecase.PayResultBroadCastMgr
import com.oplus.pay.trade.utils.Constants
import com.oplus.pay.trade.utils.viewModelsFactory
import com.oplus.pay.trade.viewmodel.ContainerViewModel
import com.oplus.pay.trade.viewmodel.ShareStatusViewModel
import com.oplus.pay.trade.viewmodel.StaticViewModel
import com.oplus.pay.ui.BaseActivity
import com.oplus.pay.ui.recomBine
import com.oplus.pay.ui.util.UiHelper
import com.oplus.pay.ui.util.menuItemClick
import com.oplus.pay.ui.util.navigationClick
import com.oplus.pay.trade.polling.TradeCenterPollingObserver
import com.oplus.pay.trade.polling.PaymentPollingManager
import com.oplus.pay.outcomes.OutcomesHelper
import com.oplus.pay.outcomes.OutcomesParam
import com.oplus.pay.outcomes.model.OutcomesPrePayResponse
import org.json.JSONObject
import java.lang.ref.SoftReference


class AcrossTradeCenterFragment : Fragment() {
    private lateinit var viewDataBinding: OpayFtTradeCenterFragmentAcrossTradeCenterBinding
    private val payReq: PayRequest by lazy {
        arguments?.getString(TradeCenterRouter.EXTRA_PAY_REQUEST)?.let {
            PayRequest.parseJson(it)
        } ?: payReq
    }
    private val shareStatusViewModel: ShareStatusViewModel by lazy(LazyThreadSafetyMode.NONE) {
        ViewModelProvider(requireActivity())[ShareStatusViewModel::class.java]
    }

    //view model
    private val viewModel by viewModelsFactory { ContainerViewModel() }
    private lateinit var payResultBroadCastMgr: PayResultBroadCastMgr

    private val staticViewModel: StaticViewModel by viewModels()

    /**
     * 支付反查观察者
     */
    private var pollingObserver: TradeCenterPollingObserver? = null

    class EventBroadCastDealer(
        val payReq: PayRequest,
        val shareStatusViewModel: ShareStatusViewModel,
        private val ref: SoftReference<Activity>,
        val fragment: SoftReference<Fragment>
    ) {
        /**
         * 支付结果处理关闭收银台
         */
        fun onReceiveResult(
            processToken: String?, payOrder: String?, outcomesCode: OutcomesCode?
        ) {
            if (OutcomesCode.CODE_CANCELED == outcomesCode) {
                return
            }
            if (TextUtils.isEmpty(shareStatusViewModel.payRequestId.value)) {
                if (payReq.processToken == processToken && isAdd()) {
                    ref.get()?.finish()
                }
            } else {
                if (payOrder == shareStatusViewModel.payRequestId.value && isAdd()) {
                    ref.get()?.finish()
                }
            }
        }

        private fun isAdd(): Boolean {
            return fragment.get()?.isAdded == true && ref.get() != null
        }
    }

    companion object {
        const val TAG = "AcrossTradeCenterFragment"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View? {
        hideSystemUI()
        viewDataBinding =
            OpayFtTradeCenterFragmentAcrossTradeCenterBinding.inflate(inflater, container, false)
        return viewDataBinding.root
    }
    override fun onActivityCreated(savedInstanceState: Bundle?) {
        super.onActivityCreated(savedInstanceState)
        UiHelper.doShowAnimation(viewDataBinding.frameLayout)
        initData()
        loadPayTypesCard()
        initQuitDialogObserver()
        initPollingObserver()
        loadData()
        setupToolbar()
        registerBroadCast()
        tradeCenterExposure()
        parseData()
        initView()
        loadPayActionCard()
        loadPayInfoCard()
        if (!payReq.isRecharge) {//充值界面
            loadBuyPlaceCard()
        }
    }

    private fun hideSystemUI() {
        requireActivity().window.setFlags(
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN,
            WindowManager.LayoutParams.FLAG_LAYOUT_NO_LIMITS or
                    WindowManager.LayoutParams.FLAG_FULLSCREEN
        )
    }

    private fun registerBroadCast() {
        if (this::payResultBroadCastMgr.isInitialized) {
            viewModel.registerBroadCast(payResultBroadCastMgr, requireContext())
        }
    }

    private fun initData() {
        payResultBroadCastMgr = PayResultBroadCastMgr(
            SoftReference(
                EventBroadCastDealer(
                    payReq,
                    shareStatusViewModel,
                    SoftReference(requireActivity()),
                    SoftReference(this)
                )::onReceiveResult
            )
        )
        shareStatusViewModel.setPayRequest(payReq)
    }

    private fun setupToolbar() {
        getToolBar()?.let {
            if (isNewToolBar()) {
                it.isTitleCenterStyle = true
            }
            (activity as AppCompatActivity).run {
                setSupportActionBar(getToolBar())
                supportActionBar?.let { actionBar ->
                    if (isNewToolBar()) {
                        actionBar.setDisplayHomeAsUpEnabled(false)
                    } else {
                        actionBar.setDisplayHomeAsUpEnabled(true)
                    }
                }
            }

            if (!isNewToolBar()) {
                getToolBar()?.navigationClick {
                    back()
                }
            }
            (activity as BaseActivity).initMenu(getMenuId())
            getToolBar()?.menuItemClick { item -> processMenuItemClick(item) }

        }
    }

    private fun processMenuItemClick(item: MenuItem?): Boolean {
        when (item?.itemId) {
            R.id.menu_setting -> {
                val defaultPayType =
                    arguments?.getString(TradeCenterRouter.EXTRA_FAST_PAY_TYPE) ?: "unknown"
                val fragment = findFragmentByTag("PayTypesCardFragment") as PayTypesCardFragment
                val isHaveBankPay = fragment.isHaveBankPay()
                val bundle = viewModel.getGoToSettingUIBundle(
                    payReq,
                    shareStatusViewModel.actualAmount.value ?: "",
                    defaultPayType,
                    isHaveBankPay,
                    ScreenType.ACROSSSCREEN.type
                )
                ARouter.getInstance().build(SettingsRouter.PATH_PATH_MAIN)
                    .with(bundle.recomBine(payReq.mPayId)).navigation(requireActivity())

                viewModel.fullTradeCenterJumpSetting(payReq)
                viewModel.settingClick(payReq)
                return true
            }

            R.id.setting -> {
                if (!isNeedHideSettingEntrence()) {
                    gotoSettingUI()
                }
                return true
            }

            R.id.cancel -> {
                cancel()
                return true
            }

            else -> return false
        }
    }
    private fun cancel() {
        alertQuit()
    }
    private fun loadData() {
        viewModel.loadBannerInfo(payReq, ScreenType.ACROSSSCREEN.type)
    }

    private var quitDialogObserver: QuitDialogObserver? = null
    private fun addQuitDialogObserver() {
        quitDialogObserver = QuitDialogObserver(
            mActivity = requireActivity(),
            viewModel = viewModel,
            shareStatusViewModel = shareStatusViewModel,
            payReq = payReq,
            alertQuitSelectVoucher = ::alertQuitSelectVoucher,
            normalBack = ::normalBack,
            payCenterOutDialogClick = ::payCenterOutDialogClick,
            getScreenType()
        )
        quitDialogObserver?.let {
            lifecycle.addObserver(
                it
            )
        }
    }

    private fun resetToolBar() {
        val orientation = resources?.configuration?.orientation
        when (orientation) {
            Configuration.ORIENTATION_PORTRAIT -> {//竖屏
            }

            Configuration.ORIENTATION_LANDSCAPE -> {//竖屏
                setupToolbar()
            }
        }
    }

    private fun initQuitDialogObserver() {
        addQuitDialogObserver()
        shareStatusViewModel.screenRotation.observe(viewLifecycleOwner) {
            //屏幕旋转推出弹框处理
            if (it != null) {
                resetToolBar()
                quitDialogObserver?.getQuitDialog()?.dismiss()
                if (isShowQuitDilaog) {
                    quitDialogObserver?.let { quitDialogObserver ->
                        lifecycle.removeObserver(quitDialogObserver)
                    }
                    addQuitDialogObserver()
                }
            }
        }
    }

    /**
     * 初始化支付反查观察者
     */
    private fun initPollingObserver() {
        try {
            pollingObserver = TradeCenterPollingObserver(
                fragmentRef = SoftReference(this),
                shareStatusViewModel = shareStatusViewModel,
                pollingCallback = object : PaymentPollingManager.PollingCallback {
                    override fun onPollingSuccess(result: OutcomesPrePayResponse?) {
                        PayLogUtil.i(TAG, "initPollingObserver: polling success, jumping to result page")
                        // 反查成功，跳转到支付结果页
                        jumpToPaymentResultPage()
                    }

                    override fun onPollingFailed(errorCode: String?, errorMsg: String?) {
                        PayLogUtil.w(TAG, "initPollingObserver: polling failed - $errorCode: $errorMsg")
                        // 反查失败，不做特殊处理，用户可以手动操作
                    }
                }
            )

            pollingObserver?.let { observer ->
                lifecycle.addObserver(observer)
                PayLogUtil.i(TAG, "initPollingObserver: polling observer added to lifecycle")
            }
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "initPollingObserver: failed to initialize polling observer", e)
        }
    }

    /**
     * 跳转到支付结果页
     */
    private fun jumpToPaymentResultPage() {
        try {
            val payRequest = shareStatusViewModel.payRequest.value
            val payRequestId = shareStatusViewModel.payRequestId.value
            val currentChannel = shareStatusViewModel.currentChannel.value

            if (payRequest != null && !payRequestId.isNullOrEmpty() && currentChannel != null) {
                val outcomesParam = OutcomesParam(
                    channelId = currentChannel.paymentCode ?: "",
                    payOrder = payRequestId,
                    bizExt = BizExt(
                        countryCode = payReq.mCountryCode ?: "",
                        partnerCode = payReq.mPartnerId ?: "",
                        partnerOrder = payReq.mPartnerOrder ?: "",
                        screenType = getScreenType(),
                        processToken = payReq.processToken,
                        source = payReq.mSource ?: "",
                        prePayToken = payReq.prePayToken
                    ),
                    appPackage = requireActivity().packageName
                )

                PayLogUtil.i(TAG, "jumpToPaymentResultPage: showing result page for order $payRequestId")
                OutcomesHelper.showResult(requireActivity(), outcomesParam)
            } else {
                PayLogUtil.w(TAG, "jumpToPaymentResultPage: missing required data for result page")
            }
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "jumpToPaymentResultPage: failed to jump to result page"+e.message)
        }
    }

    private fun parseData() {
        if (viewModel.isSelectVoucherBack(arguments)) {
            shareStatusViewModel.isSelectVoucherBack.value = true
            shareStatusViewModel.currentVoucher.value =
                arguments?.getSerializable(Constants.ETRA_SELECT_CURRENT_VOU) as? Voucher  //刷新当前券
        }
        if (arguments?.getSerializable(Constants.KEY_VOUCHER_RECIVED) as? Voucher != null) {
            shareStatusViewModel.reciveVoucher.value =
                arguments?.getSerializable(Constants.KEY_VOUCHER_RECIVED) as? Voucher
        }
    }

    private fun payCenterOutDialogClick(dialogClickType: String, from: String, trackId: String) {
        viewModel.payCenterOutDialogClick(payReq, getScreenType(), dialogClickType, from, trackId)
    }

    private fun normalBack() {
        viewModel.notifyCancel(payReq, ScreenType.ACROSSSCREEN.type)
        PayCenterHelper.cleanOrderInfo(payReq.mPartnerOrder)
        activity?.finish()
    }

    /**
     * 退出弹窗优惠券确认处理，不做抽象，页面需求自行重写
     */
    private fun alertQuitSelectVoucher(voucher: Voucher) {
        shareStatusViewModel.currentVoucher.value = voucher
    }

    private fun tradeCenterExposure() {
        if (payReq.isRecharge) {
            viewModel.kokoCoinCharge(payReq)
        } else {
            viewModel.tradeCenterExposure(payReq, getScreenType())
        }

        staticViewModel.tradeCenterPayCenterExposure(
            payRequest = payReq,
            bizNode = BizNode.TRANSACTION_CONFIRMATION.value,
            bizCode = TransactionConfirmationStatusCodes.CODE_02_000_0001.statusCode,
            bizResult = BizResult.SUCCESS.value,
            screenType = getScreenType()
        )
    }

    private fun <T> loadCard(containerViewId: Int, fragment: T, mTag: String) {
        val beginTransaction = childFragmentManager.beginTransaction()
        beginTransaction.replace(containerViewId, fragment as Fragment, mTag)
        beginTransaction.commitNowAllowingStateLoss()
    }

    private fun findFragmentByTag(mTag: String): Fragment? {
        return childFragmentManager.findFragmentByTag(mTag)
    }
    private fun loadPayActionCard() {
        loadCard(R.id.fragment_pay_action, PayActionCardFragment(), "PayActionCardFragment")
    }
    private fun loadPayInfoCard() {
        loadCard(R.id.fragment_pay_info, PayInfoCardFragment(), "PayInfoCardFragment")
    }

    private fun loadPayTypesCard() {
        if (!payReq.isAutoRenew) {
            loadCard(
                R.id.fragment_channel_list,
                QuickPayTypesCardFragment.newInstance(QuickPayTypesCardFragment.QUICK_PAY_ENABLE),
                "QuickPayTypesCardFragment_QUICK_PAY_ENABLE"
            )
            loadCard(
                R.id.fragment_channel_list_disable,
                QuickPayTypesCardFragment.newInstance(QuickPayTypesCardFragment.QUICK_PAY_DISABLE),
                "QuickPayTypesCardFragment_QUICK_PAY_DISABLE"
            )
        }
        loadCard(
            R.id.fragment_channel_list_normal, PayTypesCardFragment(), "PayTypesCardFragment"
        )
    }

    private fun loadBuyPlaceCard() {
        val combineOrderInfo: CombineOrderInfo? = arguments?.getParcelable(PRE_PARAM_BUY_PLACE)
        if (combineOrderInfo != null) {
            loadCard(
                R.id.fragment_buy_place_across, AcrossBuyPlaceFragment(), "AcrossBuyPlaceFragment"
            )
        }
    }

    override fun onPause() {
        super.onPause()
        viewModel.paycenterTime(payReq, getScreenType())
    }

    override fun onResume() {
        super.onResume()
        requireActivity().overridePendingTransition(0, 0)
        // Check if toolbar needs to be restored after recreation
        if (getToolBar()?.menu?.size == 0) {
            setupToolbar()
        }
    }

    private fun initView() {
        //  fix Cannot add the same observer with different lifecyclesat androidx.lifecycle.LiveData.observe
        viewModel.bannerInfo.observe(viewLifecycleOwner, object : Observer<Resource<Banner>> {
            override fun onChanged(t: Resource<Banner>?) {
                // nothing
            }

        })
        //fix Cannot add the same observer with different lifecyclesat androidx.lifecycle.LiveData.observe
        shareStatusViewModel.attachCheckBox.observe(viewLifecycleOwner, object : Observer<Boolean> {
            override fun onChanged(it: Boolean) {
                viewModel.quitRequest.value = true
            }
        })
        shareStatusViewModel.empty.observe(viewLifecycleOwner) {
            if (it) {
                // 渠道列表因为网络原因获取失败
                val errorGroup =
                    viewDataBinding.layoutNoData.findViewById<COUIEmptyStateView>(com.oplus.pay.ui.R.id.empty_view_group)
                setErrorViewHigh(errorGroup, -10f)
                viewDataBinding.layoutNoData.visibility = View.VISIBLE
            } else {
                viewDataBinding.layoutNoData.visibility = View.GONE
            }
        }
        shareStatusViewModel.error.observe(viewLifecycleOwner) {
            if (it) {
                createView()
            } else {
                viewGroup?.let {
                    viewDataBinding.rightLayout.removeView(viewGroup)
                }
            }
        }
        requireActivity().onBackPressedDispatcher.addCallback(viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    alertQuit()
                }
            })
    }

    private var viewGroup: ViewGroup? = null
    private fun createView() {
        viewGroup = LayoutInflater.from(activity)
            .inflate(R.layout.opay_lib_ui_layout_net_error, null) as ViewGroup
        viewGroup?.findViewById<COUIEmptyStateView>(R.id.empty_view_group)
            ?.setOnButtonClickListener {
                LocalBroadcastManager.getInstance(requireContext()).sendBroadcast(Intent().apply {
                    action = ACTION_UNBIND_BANK
                })
            }
        viewDataBinding.rightLayout.addView(viewGroup)
        val layoutParams = viewGroup?.layoutParams as RelativeLayout.LayoutParams
        layoutParams.topMargin = DisplayHelper.dip2px(requireContext(), -15f)
        viewGroup?.layoutParams = layoutParams

    }

    private fun setErrorViewHigh(stateView: COUIEmptyStateView, height: Float) {
        val layoutParams = stateView.layoutParams as LinearLayout.LayoutParams
        layoutParams.topMargin = DisplayHelper.dip2px(stateView.context, height)
        stateView.layoutParams = layoutParams
    }

    /**
     * 挽留弹窗逻辑处理
     */
    private fun alertQuit() {
        if (!viewModel.quitDialog.hasObservers()) {
            PayLogUtil.w(TAG, "quitDialog hasObservers size = 0")
            activity?.finish()
        }
        viewModel.quitDialog.value = true
    }

    private fun gotoSettingUI() {
        viewModel.settingClick(payReq)
        val defaultPayType =
            arguments?.getString(TradeCenterRouter.EXTRA_FAST_PAY_TYPE) ?: "unknown"
        val fragment = findFragmentByTag("PayTypesCardFragment") as PayTypesCardFragment
        val isHaveBankPay = fragment.isHaveBankPay()
        val bundle = viewModel.getGoToSettingUIBundle(
            payReq,
            shareStatusViewModel.actualAmount.value ?: "",
            defaultPayType,
            isHaveBankPay,
            ScreenType.ACROSSSCREEN.type
        )
        ARouter.getInstance().build(SettingsRouter.PATH_PATH_MAIN)
            .with(bundle.recomBine(payReq.mPayId)).navigation(activity)
    }

    private fun back() {
        alertQuit()
    }

    private fun isNewToolBar(): Boolean {
        return true
    }

    private fun isNeedHideSettingEntrence(): Boolean {
        if (!TextUtils.isEmpty(payReq.extraInfo)) {
            try {
                val chargeScene = JSONObject(payReq.extraInfo).optString("charge_scene")
                return !TextUtils.isEmpty(chargeScene)
            } catch (e: Exception) {
            }
        }
        return false
    }

    private fun getMenuId(): Int {
        return if (isNeedHideSettingEntrence()) {
            R.menu.opay_ft_trade_center_action_menu_without_setting
        } else {
            R.menu.opay_ft_trade_center_action_menu_half
        }
    }

    fun getToolBar(): COUIToolbar? {
        return viewDataBinding.titleContainer.findViewById<COUIToolbar>(com.oplus.pay.ui.R.id.toolbar)
            .apply {
                title = if (payReq.isRecharge && isNeedHideSettingEntrence()) {
                    getString(R.string.opay_paysub_kebi_recharge)
                } else {
                    getString(R.string.pay_center_new)
                }
            }
    }

    private fun getScreenType(): String = ScreenType.ACROSSSCREEN.type

    override fun onDestroy() {
        super.onDestroy()

        // 清理反查观察者
        pollingObserver?.let { observer ->
            lifecycle.removeObserver(observer)
            PayLogUtil.i(TAG, "onDestroy: polling observer removed from lifecycle")
        }
        pollingObserver = null
    }
}