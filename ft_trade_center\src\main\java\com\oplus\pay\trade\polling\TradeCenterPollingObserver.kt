package com.oplus.pay.trade.polling

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.oplus.pay.basic.PayLogUtil
import com.oplus.pay.outcomes.model.OutcomesPrePayResponse
import com.oplus.pay.trade.viewmodel.ShareStatusViewModel
import java.lang.ref.WeakReference

/**
 * 收银台反查观察者
 * 负责在收银台Fragment的onResume时触发支付结果反查
 * 
 * 功能：
 * 1. 监听Fragment生命周期，在onResume时检查是否需要反查
 * 2. 判断当前支付渠道是否为目标反查渠道
 * 3. 检查订单状态，避免重复反查
 * 4. 执行反查逻辑，显示Loading，处理结果
 */
class TradeCenterPollingObserver(
    private val fragmentRef: WeakReference<Any>, // Fragment的弱引用
    private val shareStatusViewModel: ShareStatusViewModel,
    private val pollingCallback: PaymentPollingManager.PollingCallback
) : DefaultLifecycleObserver {
    
    private val TAG = "TradeCenterPollingObserver"
    
    /**
     * 收银台实例ID，用于状态隔离
     */
    private var tradeCenterInstanceId: String? = null
    

    
    /**
     * 是否正在反查中
     */
    private var isPollingInProgress = false
    
    init {
        // 注册收银台实例到状态管理器
        val fragment = fragmentRef.get()
        if (fragment != null) {
            val processToken = shareStatusViewModel.payRequest.value?.processToken ?: ""
            tradeCenterInstanceId = PaymentPollingStatusManager.registerTradeCenterInstance(processToken, fragment)
            PayLogUtil.i(TAG, "TradeCenterPollingObserver created with instanceId: $tradeCenterInstanceId")
        }
    }
    
    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        PayLogUtil.i(TAG, "onResume: checking if polling is needed")
        
        // 检查是否需要进行反查
        if (shouldStartPolling()) {
            startPolling()
        }
    }
    
    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
        PayLogUtil.i(TAG, "onDestroy: cleaning up")

        // 清理资源
        tradeCenterInstanceId?.let { instanceId ->
            PaymentPollingStatusManager.unregisterTradeCenterInstance(instanceId)
        }
        isPollingInProgress = false
    }

    /**
     * 获取当前订单号
     * 优先使用payRequestId，如果为空则使用contractCode（签约模式）
     * @return 订单号，如果都为空则返回null
     */
    private fun getCurrentOrderId(): String? {
        val payRequestId = shareStatusViewModel.payRequestId.value
        val contractCode = shareStatusViewModel.contractCode.value

        return when {
            !payRequestId.isNullOrEmpty() -> {
                PayLogUtil.d(TAG, "getCurrentOrderId: using payRequestId=$payRequestId")
                payRequestId
            }
            !contractCode.isNullOrEmpty() -> {
                PayLogUtil.d(TAG, "getCurrentOrderId: using contractCode=$contractCode (contract mode)")
                contractCode
            }
            else -> {
                PayLogUtil.d(TAG, "getCurrentOrderId: both payRequestId and contractCode are empty")
                null
            }
        }
    }

    /**
     * 检查是否应该开始反查
     */
    private fun shouldStartPolling(): Boolean {
        // 1. 检查是否正在反查中
        if (isPollingInProgress) {
            PayLogUtil.i(TAG, "shouldStartPolling: polling already in progress")
            return false
        }
        
        // 2. 检查是否有订单号（下单成功的标志）
        val orderId = getCurrentOrderId()
        if (orderId.isNullOrEmpty()) {
            PayLogUtil.d(TAG, "shouldStartPolling: no orderId (payRequestId or contractCode), order not placed yet")
            return false
        }
        
        // 3. 检查当前支付渠道是否为目标反查渠道
        val currentChannel = shareStatusViewModel.currentChannel.value
        if (!PaymentChannelTypeChecker.shouldPollPaymentResult(currentChannel)) {
            PayLogUtil.d(TAG, "shouldStartPolling: channel does not need polling - ${PaymentChannelTypeChecker.getChannelDetailInfo(currentChannel)}")
            return false
        }
        
        // 4. 检查是否已经反查过
        val instanceId = tradeCenterInstanceId
        if (instanceId == null) {
            PayLogUtil.w(TAG, "shouldStartPolling: instanceId is null")
            return false
        }
        
        val processToken = shareStatusViewModel.payRequest.value?.processToken ?: ""
        if (!PaymentPollingStatusManager.shouldStartPolling(instanceId, orderId, processToken)) {
            PayLogUtil.i(TAG, "shouldStartPolling: order already polled or in progress")
            return false
        }
        
        PayLogUtil.i(TAG, "shouldStartPolling: all conditions met, should start polling for orderId=$orderId")
        return true
    }
    
    /**
     * 开始反查
     */
    private fun startPolling() {
        val orderId = shareStatusViewModel.payRequestId.value ?: ""
        val contractCode = shareStatusViewModel.contractCode.value?: ""
        val currentChannel = shareStatusViewModel.currentChannel.value ?: return
        val processToken = shareStatusViewModel.payRequest.value?.processToken ?: ""
        val instanceId = tradeCenterInstanceId ?: return
        val fragment = fragmentRef.get()

        if (fragment == null) {
            PayLogUtil.e(TAG, "startPolling: fragment reference is null")
            handlePollingFailed(orderId, "FRAGMENT_NULL", "Fragment reference is null")
            return
        }

        PayLogUtil.i(TAG, "startPolling: starting polling for orderId=$orderId, channel=${currentChannel.paymentCode}")
        PayLogUtil.d(TAG, "startPolling: fragment=${fragment.javaClass.simpleName}, instanceId=$instanceId")

        // 标记开始反查
        PaymentPollingStatusManager.markPollingStarted(instanceId, orderId, processToken)
        isPollingInProgress = true

        // 使用PaymentPollingManager执行反查
        val activity = getActivityFromFragment()
        if (activity != null) {
            val activityRef = java.lang.ref.SoftReference(activity)
            val pollingManager = PaymentPollingManager(
                activityRef = activityRef,
                shareStatusViewModel = shareStatusViewModel,
                lifecycleOwner = fragment as LifecycleOwner
            )

            pollingManager.startPolling(
                payResult = shareStatusViewModel.payRequest.value,
                orderId = orderId,
                channel = currentChannel,
                contractCode =contractCode,
                processToken = processToken,
                callback = object : PaymentPollingManager.PollingCallback {
                    override fun onPollingSuccess(result:OutcomesPrePayResponse?) {
                        handlePollingSuccess(orderId, result)
                    }

                    override fun onPollingFailed(errorCode: String?, errorMsg: String?) {
                        handlePollingFailed(orderId, errorCode, errorMsg)
                    }
                }
            )
        } else {
            PayLogUtil.e(TAG, "startPolling: activity reference is null")
            handlePollingFailed(orderId, "ACTIVITY_NULL", "Activity reference is null")
        }
    }
    
    /**
     * 处理反查成功
     */
    private fun handlePollingSuccess(orderId: String, result:OutcomesPrePayResponse?) {
        PayLogUtil.i(TAG, "handlePollingSuccess: orderId=$orderId, result=$result")

        val instanceId = tradeCenterInstanceId
        if (instanceId != null) {
            PaymentPollingStatusManager.markPollingSuccess(instanceId, orderId)
        }

        isPollingInProgress = false

        // 执行成功回调 - 跳转到支付结果页
        pollingCallback.onPollingSuccess(result)
    }
    
    /**
     * 处理反查失败
     */
    private fun handlePollingFailed(orderId: String, errorCode: String?, errorMsg: String?) {
        PayLogUtil.w(TAG, "handlePollingFailed: orderId=$orderId, errorCode=$errorCode, errorMsg=$errorMsg")

        val instanceId = tradeCenterInstanceId
        if (instanceId != null) {
            // 判断是否可以重试
            val canRetry = isRetryableError(errorCode)
            PaymentPollingStatusManager.markPollingFailed(instanceId, orderId, canRetry)
        }

        isPollingInProgress = false

        // 执行失败回调 - 隐藏Loading，不做其他处理
        pollingCallback.onPollingFailed(errorCode, errorMsg)
    }

    /**
     * 从Fragment获取Activity
     * @return FragmentActivity实例，如果获取失败返回null
     */
    private fun getActivityFromFragment(): androidx.fragment.app.FragmentActivity? {
        return try {
            val fragment = fragmentRef.get()
            PayLogUtil.d(TAG, "getActivityFromFragment: fragment=$fragment, type=${fragment?.javaClass?.simpleName}")

            when (fragment) {
                is androidx.fragment.app.Fragment -> {
                    val activity = fragment.activity
                    PayLogUtil.d(TAG, "getActivityFromFragment: activity=$activity, type=${activity?.javaClass?.simpleName}")
                    activity as? androidx.fragment.app.FragmentActivity
                }
                else -> {
                    PayLogUtil.w(TAG, "getActivityFromFragment: fragment is not androidx.fragment.app.Fragment, type=${fragment?.javaClass?.simpleName}")
                    null
                }
            }
        } catch (e: Exception) {
            PayLogUtil.e(TAG, "getActivityFromFragment: exception"+e.message)
            null
        }
    }

    /**
     * 判断错误是否可以重试
     * @param errorCode 错误代码
     * @return true表示可以重试，false表示不可重试
     */
    private fun isRetryableError(errorCode: String?): Boolean {
        return when (errorCode) {
            // 可重试的错误类型
            "ACTIVITY_NULL" -> true           // Activity为空，可能下次onResume时Activity已恢复
            "FRAGMENT_NULL" -> true           // Fragment为空，可能下次onResume时Fragment已恢复
            "NETWORK_ERROR" -> true           // 网络错误，可能网络恢复后可以重试
            "TIMEOUT" -> true                 // 超时错误，可以重试
            "EXCEPTION" -> true               // 一般异常，可以重试
            "CHANNEL_QUERY_FAILED" -> true    // 渠道查询失败，可能是临时问题

            // 不可重试的错误类型
            "ORDER_NOT_FOUND" -> false        // 订单不存在，重试无意义
            "INVALID_PARAMS" -> false         // 参数错误，重试无意义
            "BUSINESS_ERROR" -> false         // 业务逻辑错误，重试无意义

            // 默认情况：未知错误代码，允许重试
            else -> true
        }
    }

    /**
     * 手动触发反查（用于测试或特殊场景）
     */
    fun manualTriggerPolling() {
        PayLogUtil.i(TAG, "manualTriggerPolling: manually triggering polling")
        if (shouldStartPolling()) {
            startPolling()
        }
    }
    
    /**
     * 获取当前反查状态
     */
    fun getCurrentPollingStatus(): PaymentPollingStatusManager.PollingStatus? {
        val orderId = getCurrentOrderId() ?: return null
        val instanceId = tradeCenterInstanceId ?: return null
        return PaymentPollingStatusManager.getPollingStatus(instanceId, orderId)
    }
    
    /**
     * 重置反查状态（用于重新反查）
     */
    fun resetPollingStatus() {
        val orderId = getCurrentOrderId() ?: return
        val instanceId = tradeCenterInstanceId ?: return

        PayLogUtil.i(TAG, "resetPollingStatus: resetting status for orderId=$orderId")

        // 从状态管理器中移除该订单的状态
        PaymentPollingStatusManager.pollingStatusMap[instanceId]?.remove(orderId)
        isPollingInProgress = false
    }
    
    /**
     * 获取调试信息
     */
    fun getDebugInfo(): String {
        val payRequestId = shareStatusViewModel.payRequestId.value
        val contractCode = shareStatusViewModel.contractCode.value
        val orderId = getCurrentOrderId()
        val currentChannel = shareStatusViewModel.currentChannel.value
        val status = getCurrentPollingStatus()

        return "TradeCenterPollingObserver Debug Info:\n" +
                "instanceId: $tradeCenterInstanceId\n" +
                "payRequestId: $payRequestId\n" +
                "contractCode: $contractCode\n" +
                "currentOrderId: $orderId\n" +
                "isPollingInProgress: $isPollingInProgress\n" +
                "currentStatus: $status\n" +
                "channel: ${PaymentChannelTypeChecker.getChannelDetailInfo(currentChannel)}\n" +
                "shouldPoll: ${shouldStartPolling()}"
    }
}
